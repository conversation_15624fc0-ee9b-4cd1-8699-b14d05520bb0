<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from "vue"
import * as fabric from 'fabric'
import LoadingOverlay from './LoadingOverlay.vue'

defineOptions({
  name: "FabricCanvas"
})

interface Props {
  showGrid: boolean
  isLoading: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  canvasReady: [canvas: fabric.Canvas]
  zoomChanged: [zoom: number]
}>()

// Canvas相关引用
const canvasRef = ref<HTMLCanvasElement>()
const canvasContainer = ref<HTMLDivElement>()
let fabricCanvas: fabric.Canvas | null = null

// 画布状态
const canvasReady = ref(false)
const zoomLevel = ref(1)
const minZoom = 0.1
const maxZoom = 5

// 画布配置
const canvasConfig = {
  backgroundColor: '#f5f5f5'
}

// 网格配置
const gridConfig = {
  size: 20,
  color: '#ddd',
  strokeWidth: 1
}

// 动态计算画布尺寸
const getCanvasSize = () => {
  if (!canvasContainer.value) {
    return { width: 1000, height: 700 }
  }

  const container = canvasContainer.value
  const containerWidth = container.clientWidth
  const containerHeight = container.clientHeight

  if (containerWidth === 0 || containerHeight === 0) {
    return { width: 1000, height: 700 }
  }

  const padding = 20
  const width = Math.max(400, containerWidth - padding)
  const height = Math.max(300, containerHeight - padding)

  return { width, height }
}

// 等待容器尺寸就绪
const waitForContainerSize = async (maxRetries = 10): Promise<{ width: number, height: number }> => {
  for (let i = 0; i < maxRetries; i++) {
    const size = getCanvasSize()

    if (size.width > 400 && size.height > 300) {
      return size
    }

    await new Promise(resolve => setTimeout(resolve, 200))
  }

  return { width: 1000, height: 700 }
}

// 绘制网格
const drawGrid = () => {
  if (!fabricCanvas) return

  // 清除现有网格
  const existingGrid = fabricCanvas.getObjects().filter((obj: any) => (obj as any).name === 'grid-line')
  existingGrid.forEach((line: any) => fabricCanvas!.remove(line))

  if (!props.showGrid) return

  const { size, color, strokeWidth } = gridConfig

  // 获取画布实际尺寸
  const canvasWidth = fabricCanvas.getWidth()
  const canvasHeight = fabricCanvas.getHeight()

  // 扩大网格范围
  const multiplier = 5
  const gridLeft = -canvasWidth * multiplier
  const gridTop = -canvasHeight * multiplier
  const gridRight = canvasWidth * multiplier
  const gridBottom = canvasHeight * multiplier

  // 计算网格起始位置
  const startX = Math.floor(gridLeft / size) * size
  const startY = Math.floor(gridTop / size) * size

  // 绘制垂直线
  for (let x = startX; x <= gridRight; x += size) {
    const line = new (fabric as any).Line([x, gridTop, x, gridBottom], {
      stroke: color,
      strokeWidth: strokeWidth,
      selectable: false,
      evented: false,
      name: 'grid-line',
      excludeFromExport: true
    })
    fabricCanvas.add(line)
  }

  // 绘制水平线
  for (let y = startY; y <= gridBottom; y += size) {
    const line = new (fabric as any).Line([gridLeft, y, gridRight, y], {
      stroke: color,
      strokeWidth: strokeWidth,
      selectable: false,
      evented: false,
      name: 'grid-line',
      excludeFromExport: true
    })
    fabricCanvas.add(line)
  }

  // 将网格移到最底层
  const gridLines = fabricCanvas.getObjects().filter((obj: any) => (obj as any).name === 'grid-line')
  gridLines.forEach((line: any) => {
    if (fabricCanvas && typeof (fabricCanvas as any).sendObjectToBack === 'function') {
      (fabricCanvas as any).sendObjectToBack(line)
    }
  })

  fabricCanvas.renderAll()
}

// 更新网格（防抖处理）
let gridUpdateTimer: number | null = null
const updateGrid = () => {
  if (gridUpdateTimer) {
    clearTimeout(gridUpdateTimer)
  }

  gridUpdateTimer = setTimeout(() => {
    drawGrid()
    gridUpdateTimer = null
  }, 100) as unknown as number
}

// 添加事件监听
const addEventListeners = () => {
  if (!fabricCanvas) return
  
  // 鼠标滚轮缩放
  fabricCanvas.on('mouse:wheel', (opt) => {
    const delta = opt.e.deltaY
    let zoom = fabricCanvas!.getZoom()
    zoom *= 0.999 ** delta

    if (zoom > maxZoom) zoom = maxZoom
    if (zoom < minZoom) zoom = minZoom

    fabricCanvas!.zoomToPoint(new (fabric as any).Point(opt.e.offsetX, opt.e.offsetY), zoom)
    zoomLevel.value = Math.round(zoom * 100) / 100
    emit('zoomChanged', zoomLevel.value)

    updateGrid()

    opt.e.preventDefault()
    opt.e.stopPropagation()
  })
  
  // 画布拖拽
  let isDragging = false
  let lastPosX = 0
  let lastPosY = 0

  fabricCanvas.on('mouse:down', (opt: any) => {
    const evt = opt.e
    if (evt.altKey === true) {
      isDragging = true
      fabricCanvas!.selection = false
      lastPosX = evt.clientX
      lastPosY = evt.clientY
    }
  })

  fabricCanvas.on('mouse:move', (opt: any) => {
    if (isDragging) {
      const e = opt.e
      const vpt = fabricCanvas!.viewportTransform!
      vpt[4] += e.clientX - lastPosX
      vpt[5] += e.clientY - lastPosY
      fabricCanvas!.requestRenderAll()
      lastPosX = e.clientX
      lastPosY = e.clientY
    }
  })

  fabricCanvas.on('mouse:up', () => {
    fabricCanvas!.setViewportTransform(fabricCanvas!.viewportTransform!)
    isDragging = false
    fabricCanvas!.selection = true
  })
}

// 初始化画布
const initCanvas = async () => {
  if (!canvasRef.value || !canvasContainer.value) return

  try {
    // 等待容器尺寸就绪
    const { width, height } = await waitForContainerSize()

    // 创建Fabric.js画布实例
    fabricCanvas = new (fabric as any).Canvas(canvasRef.value, {
      width,
      height,
      backgroundColor: canvasConfig.backgroundColor,
      selection: true,
      preserveObjectStacking: true
    })

    // 绘制网格
    drawGrid()

    // 添加事件监听
    addEventListeners()

    // 延迟更新画布尺寸
    setTimeout(() => {
      updateCanvasSize()
    }, 100)

    setTimeout(() => {
      updateCanvasSize()
    }, 500)

    canvasReady.value = true
    emit('canvasReady', fabricCanvas)

  } catch (error) {
    console.error('画布初始化失败:', error)
  }
}

// 更新画布尺寸
const updateCanvasSize = () => {
  if (!fabricCanvas || !canvasContainer.value) return

  const { width, height } = getCanvasSize()
  const currentWidth = fabricCanvas.getWidth()
  const currentHeight = fabricCanvas.getHeight()

  if (Math.abs(currentWidth - width) > 5 || Math.abs(currentHeight - height) > 5) {
    fabricCanvas.setDimensions({ width, height })
    updateGrid()
    fabricCanvas.renderAll()
  }
}

// 窗口大小变化处理
const handleResize = () => {
  nextTick(() => {
    updateCanvasSize()
  })
}

// 监听网格显示状态变化
watch(() => props.showGrid, () => {
  drawGrid()
})

// 暴露方法给父组件
defineExpose({
  fabricCanvas: () => fabricCanvas,
  updateCanvasSize,
  updateGrid,
  zoomIn: () => {
    if (!fabricCanvas) return
    let zoom = fabricCanvas.getZoom() * 1.1
    if (zoom > maxZoom) zoom = maxZoom
    fabricCanvas.setZoom(zoom)
    zoomLevel.value = Math.round(zoom * 100) / 100
    emit('zoomChanged', zoomLevel.value)
    updateGrid()
  },
  zoomOut: () => {
    if (!fabricCanvas) return
    let zoom = fabricCanvas.getZoom() / 1.1
    if (zoom < minZoom) zoom = minZoom
    fabricCanvas.setZoom(zoom)
    zoomLevel.value = Math.round(zoom * 100) / 100
    emit('zoomChanged', zoomLevel.value)
    updateGrid()
  },
  resetZoom: () => {
    if (!fabricCanvas) return
    fabricCanvas.setZoom(1)
    zoomLevel.value = 1
    emit('zoomChanged', zoomLevel.value)
    updateGrid()
  },
  fitToCanvas: () => {
    if (!fabricCanvas) return
    fabricCanvas.setZoom(1)
    zoomLevel.value = 1
    emit('zoomChanged', zoomLevel.value)
    updateGrid()
  },
  addImage: (fabricImg: any) => {
    if (!fabricCanvas) return
    fabricCanvas.add(fabricImg)
    fabricCanvas.setActiveObject(fabricImg)
    fabricCanvas.renderAll()
  },
  removeImage: (fabricImg: any) => {
    if (!fabricCanvas) return

    // 验证对象是否在画布中
    const objects = fabricCanvas.getObjects()
    const objectExists = objects.includes(fabricImg)

    if (objectExists) {
      fabricCanvas.remove(fabricImg)
      fabricCanvas.renderAll()
      console.log('图像已从画布中移除')
    } else {
      console.warn('要删除的图像对象不在画布中')
    }
  },
  removeImageById: (imageId: string) => {
    if (!fabricCanvas) return

    // 通过 imageId 查找对象
    const objects = fabricCanvas.getObjects()
    const targetObject = objects.find((obj: any) => obj.imageId === imageId)

    if (targetObject) {
      fabricCanvas.remove(targetObject)
      fabricCanvas.renderAll()
      console.log(`图像 ${imageId} 已从画布中移除`)
      return true
    } else {
      console.warn(`未找到 imageId 为 ${imageId} 的图像对象`)
      return false
    }
  },
  clearCanvas: () => {
    if (!fabricCanvas) return
    const objects = fabricCanvas.getObjects().filter((obj: any) => obj.name !== 'grid-line')
    objects.forEach((obj: any) => fabricCanvas!.remove(obj))
    fabricCanvas.renderAll()
  },
  setActiveObject: (fabricImg: any) => {
    if (!fabricCanvas) return
    fabricCanvas.setActiveObject(fabricImg)
    fabricCanvas.renderAll()
  }
})

// 组件挂载
onMounted(async () => {
  await nextTick()
  await initCanvas()
  window.addEventListener('resize', handleResize)
})

// 组件卸载
onUnmounted(() => {
  if (fabricCanvas) {
    fabricCanvas.dispose()
    fabricCanvas = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<template>
  <div
    ref="canvasContainer"
    class="canvas-container"
    :class="{ 'loading': isLoading }"
  >
    <canvas ref="canvasRef"></canvas>
    <LoadingOverlay v-if="isLoading" />
  </div>
</template>

<style lang="scss" scoped>
.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #fafafa;
  width: 100%;
  height: 100%;

  &.loading {
    pointer-events: none;
  }

  canvas {
    border: 1px solid var(--el-border-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background: white;
    display: block;
  }
}
</style>
